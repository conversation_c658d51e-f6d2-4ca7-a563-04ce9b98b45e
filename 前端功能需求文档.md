# 文案审核系统前端功能需求文档

## 目录
1. [前端功能需求清单](#1-前端功能需求清单)
2. [API接口文档](#2-api接口文档)
3. [前端功能与后端API对应关系](#3-前端功能与后端api对应关系)
4. [端口配置说明](#4-端口配置说明)
5. [前端API基地址自动解析](#5-前端api基地址自动解析)

---

## 1. 前端功能需求清单

### 1.1 用户认证模块

#### 1.1.1 登录页面 (`/login`)
- **页面功能：** 用户登录和注册入口
- **界面组件：**
  - 登录表单
    - 用户名输入框（必填，3-20位字母/数字/下划线）
    - 密码输入框（必填，最小6位）
    - 登录按钮（带加载状态）
  - 注册表单（可被管理员禁用）
    - 用户名输入框（必填，3-20位字母/数字/下划线）
    - 密码输入框（必填，最小6位）
    - 昵称输入框（必填，2-20位）
    - 注册按钮（带加载状态）
  - Tab切换（登录/注册）
- **交互功能：**
  - 表单验证（实时验证和提交验证）
  - 登录成功后跳转到指定页面或首页
  - 注册成功后自动切换到登录Tab
  - 错误信息显示
  - 注册功能开关状态检查

#### 1.1.2 修改密码页面 (`/change-password`)
- **页面功能：** 用户修改个人密码
- **界面组件：**
  - 当前密码输入框（必填）
  - 新密码输入框（必填，最小6位）
  - 确认密码输入框（必填，需与新密码一致）
  - 密码强度指示器
  - 安全提示信息
  - 提交按钮
- **交互功能：**
  - 密码强度实时检测
  - 确认密码一致性验证
  - 修改成功后提示并可选择跳转

### 1.2 主布局模块

#### 1.2.1 主布局组件 (`MainLayout.vue`)
- **页面功能：** 系统主要布局框架
- **界面组件：**
  - 顶部导航栏
    - 系统标题
    - 用户信息显示（昵称、角色）
    - 退出登录按钮
  - 侧边导航栏
    - 仪表盘链接
    - 文案管理链接
    - 用户管理链接（仅管理员可见）
    - 注册设置链接（仅管理员可见）
    - 修改密码链接
  - 主内容区域
- **交互功能：**
  - 根据用户角色动态显示菜单项
  - 退出登录确认
  - 路由导航

### 1.3 仪表盘模块

#### 1.3.1 仪表盘页面 (`/`)
- **页面功能：** 系统概览和快速操作入口
- **界面组件：**
  - 统计卡片区域
    - 总文案数量
    - 待审核文案数量
    - 已通过文案数量
    - 已拒绝文案数量
    - 总用户数量（仅管理员可见）
    - 活跃用户数量（仅管理员可见）
  - 快速操作区域
    - 文案管理按钮
    - 用户管理按钮（仅管理员可见）
    - 修改密码按钮
    - 系统设置按钮（仅管理员可见）
  - 最近活动区域
    - 最近审核活动列表
    - 活动时间显示
    - 操作类型显示
- **交互功能：**
  - 统计数据实时刷新
  - 快速跳转到对应功能页面
  - 根据用户角色显示不同内容

### 1.4 文案管理模块

#### 1.4.1 文案管理页面 (`/texts`)
- **页面功能：** 文案列表查看和审核操作
- **界面组件：**
  - 搜索和筛选区域
    - 关键词搜索框（搜索用户昵称或文案内容）
    - 状态筛选下拉框（全部/待审核/已通过/已拒绝）
    - 用户昵称筛选输入框
    - 文案长度筛选下拉框（全部/短文案/中等文案/长文案）
    - 排序方式选择（按时间/状态/ID）
    - 排序顺序选择（升序/降序）
    - 搜索按钮
    - 重置按钮
  - 批量操作区域
    - 全选复选框
    - 批量审核按钮（仅审核员及以上权限）
    - 批量状态更新下拉框
  - 文案列表表格
    - 选择复选框列
    - ID列
    - 文案内容列（支持展开/收起）
    - 作者昵称列
    - 状态列（带状态标签）
    - 提交时间列
    - 操作列（查看详情、审核按钮）
  - 分页组件
    - 页码选择
    - 每页数量选择
    - 总数显示
    - 跳转到指定页
- **交互功能：**
  - 实时搜索和筛选
  - 单个文案状态更新（仅审核员及以上权限）
  - 批量文案状态更新（仅审核员及以上权限）
  - 文案详情查看
  - 分页导航
  - 数据加载状态显示

### 1.5 用户管理模块（仅管理员）

#### 1.5.1 用户管理页面 (`/users`)
- **页面功能：** 用户账户管理
- **界面组件：**
  - 用户统计区域
    - 总用户数
    - 各角色用户数量
    - 活跃用户数
  - 操作区域
    - 添加用户按钮
    - 刷新按钮
    - 搜索输入框（搜索用户名或昵称）
    - 角色筛选下拉框
    - 状态筛选下拉框
  - 用户列表表格
    - ID列
    - 用户名列
    - 昵称列
    - 角色列（带角色标签）
    - 状态列（启用/禁用）
    - 创建时间列
    - 最后更新时间列
    - 操作列（编辑、启用/禁用、删除）
  - 分页组件
- **交互功能：**
  - 用户搜索和筛选
  - 添加新用户（弹窗表单）
  - 编辑用户信息（弹窗表单）
  - 重置用户密码
  - 启用/禁用用户账户
  - 删除用户（带确认提示）
  - 权限控制（非superadmin不能操作admin用户）

#### 1.5.2 用户编辑弹窗
- **界面组件：**
  - 用户名输入框（创建时可编辑）
  - 昵称输入框
  - 密码输入框（创建时必填）
  - 角色选择下拉框
  - 状态选择开关
  - 确认按钮
  - 取消按钮
- **交互功能：**
  - 表单验证
  - 创建/更新用户信息
  - 权限检查

### 1.6 系统设置模块（仅管理员）

#### 1.6.1 注册设置页面 (`/registration-settings`)
- **页面功能：** 控制用户注册功能开关
- **界面组件：**
  - 注册状态显示
    - 当前注册状态（开启/关闭）
    - 状态说明文字
  - 注册控制区域
    - 开启/关闭注册开关
    - 确认按钮
  - 注册统计信息
    - 总注册用户数
    - 今日注册数
    - 本周注册数
    - 本月注册数
- **交互功能：**
  - 切换注册开关状态
  - 实时更新统计信息
  - 操作确认提示

### 1.7 通用组件

#### 1.7.1 消息提示组件
- **功能：** 全局消息提示
- **类型：** 成功、警告、错误、信息
- **显示位置：** 页面顶部或右上角

#### 1.7.2 确认对话框组件
- **功能：** 危险操作确认
- **应用场景：** 删除用户、批量操作、退出登录等

#### 1.7.3 加载状态组件
- **功能：** 数据加载状态显示
- **类型：** 按钮加载、页面加载、表格加载

---

## 2. API接口文档

### 2.1 认证接口 (Auth API)

**基础路径：** `/api/auth`

#### 2.1.1 用户登录
- **接口概述：** 用户登录验证
- **请求方法：** `POST`
- **请求URL：** `/api/auth/login`
- **请求参数 (Body)：**
  ```json
  {
    "username": "string", // 必填，用户名
    "password": "string"  // 必填，密码
  }
  ```
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "data": {
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "user": {
        "id": 1,
        "username": "admin",
        "nickname": "管理员",
        "role": "admin",
        "status": 1
      }
    },
    "message": "登录成功"
  }
  ```
- **错误响应：**
  - `400 Bad Request`: `{"success": false, "message": "用户名和密码不能为空"}`
  - `401 Unauthorized`: `{"success": false, "message": "用户名或密码错误"}`
  - `403 Forbidden`: `{"success": false, "message": "账户已被禁用"}`
- **认证/授权：** 无需认证

#### 2.1.2 用户注册
- **接口概述：** 新用户注册
- **请求方法：** `POST`
- **请求URL：** `/api/auth/register`
- **请求参数 (Body)：**
  ```json
  {
    "username": "string", // 必填，3-20位字母/数字/下划线
    "password": "string", // 必填，最小6位
    "nickname": "string"  // 必填，2-20位
  }
  ```
- **响应 (成功 201 Created)：**
  ```json
  {
    "success": true,
    "data": {
      "id": 10,
      "username": "newuser",
      "nickname": "新用户",
      "role": "reviewer",
      "status": 1
    },
    "message": "注册成功"
  }
  ```
- **错误响应：**
  - `400 Bad Request`: `{"success": false, "message": "用户名已存在"}`
  - `403 Forbidden`: `{"success": false, "message": "注册功能已关闭"}`
- **认证/授权：** 无需认证

#### 2.1.3 获取注册状态
- **接口概述：** 检查注册功能是否开启
- **请求方法：** `GET`
- **请求URL：** `/api/auth/registration-status`
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "data": {
      "registrationEnabled": true
    },
    "message": "获取注册状态成功"
  }
  ```
- **认证/授权：** 无需认证

#### 2.1.4 获取当前用户信息
- **接口概述：** 获取当前登录用户的详细信息
- **请求方法：** `GET`
- **请求URL：** `/api/auth/me`
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "data": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "role": "admin",
      "status": 1,
      "created_at": "2023-10-26T10:00:00.000Z",
      "updated_at": "2023-10-26T10:00:00.000Z"
    },
    "message": "获取用户信息成功"
  }
  ```
- **错误响应：**
  - `401 Unauthorized`: `{"success": false, "message": "未登录或token已过期"}`
- **认证/授权：** 需要认证

#### 2.1.5 修改密码
- **接口概述：** 用户修改自己的密码
- **请求方法：** `POST`
- **请求URL：** `/api/auth/change-password`
- **请求参数 (Body)：**
  ```json
  {
    "currentPassword": "string", // 必填，当前密码
    "newPassword": "string"      // 必填，新密码，最小6位
  }
  ```
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "message": "密码修改成功"
  }
  ```
- **错误响应：**
  - `400 Bad Request`: `{"success": false, "message": "当前密码错误"}`
- **认证/授权：** 需要认证

#### 2.1.6 切换注册状态（管理员）
- **接口概述：** 管理员开启或关闭用户注册功能
- **请求方法：** `POST`
- **请求URL：** `/api/auth/toggle-registration`
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "data": {
      "registrationEnabled": false
    },
    "message": "注册功能已关闭"
  }
  ```
- **认证/授权：** 需要管理员权限

#### 2.1.7 获取注册统计（管理员）
- **接口概述：** 获取用户注册的统计信息
- **请求方法：** `GET`
- **请求URL：** `/api/auth/registration-stats`
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "data": {
      "total": 100,
      "today": 5,
      "thisWeek": 20,
      "thisMonth": 80
    },
    "message": "获取注册统计成功"
  }
  ```
- **认证/授权：** 需要管理员权限

### 2.2 文案接口 (Text API)

**基础路径：** `/api/texts`

#### 2.2.1 获取文案列表
- **接口概述：** 获取文案列表，支持分页、筛选和排序
- **请求方法：** `GET`
- **请求URL：** `/api/texts`
- **请求参数 (Query)：**
  | 参数名       | 数据类型 | 是否必需 | 默认值        | 描述                                     |
  | ------------ | -------- | -------- | ------------- | ---------------------------------------- |
  | `page`       | Number   | 否       | 1             | 页码                                     |
  | `size`       | Number   | 否       | 20            | 每页数量 (最大200)                       |
  | `status`     | String   | 否       |               | 按状态筛选 (`0`, `1`, `2`)               |
  | `search`     | String   | 否       |               | 关键词搜索 (匹配`user_nick`或`text`)     |
  | `userNick`   | String   | 否       |               | 按用户昵称精确筛选                       |
  | `textLength` | String   | 否       |               | 按长度筛选 (`short`, `medium`, `long`)   |
  | `sortBy`     | String   | 否       | `record_time` | 排序字段 (`record_time`, `status`, `id`) |
  | `sortOrder`  | String   | 否       | `DESC`        | 排序顺序 (`ASC`, `DESC`)                 |
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "data": {
      "list": [
        {
          "id": 1,
          "text": "这是一条文案。",
          "user_nick": "作者A",
          "status": 1,
          "record_time": "2023-10-27T10:00:00.000Z"
        }
      ],
      "pagination": {
        "page": 1,
        "size": 20,
        "total": 100,
        "pages": 5
      }
    },
    "message": "获取文案列表成功"
  }
  ```
- **认证/授权：** 需要认证

#### 2.2.2 获取单个文案
- **接口概述：** 根据ID获取单个文案的详细信息
- **请求方法：** `GET`
- **请求URL：** `/api/texts/:id`
- **请求参数 (Path)：**
  | 参数名 | 数据类型 | 描述     |
  | ------ | -------- | -------- |
  | `id`   | Integer  | 文案的ID |
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "data": {
      "id": 1,
      "text": "这是一条文案的详细内容。",
      "user_nick": "作者A",
      "status": 1,
      "record_time": "2023-10-27T10:00:00.000Z"
    },
    "message": "获取文案详情成功"
  }
  ```
- **错误响应：**
  - `404 Not Found`: `{"success": false, "message": "文案不存在"}`
- **认证/授权：** 需要认证

#### 2.2.3 更新文案状态（审核员）
- **接口概述：** 审核员或管理员更新单个文案的审核状态
- **请求方法：** `PUT`
- **请求URL：** `/api/texts/:id/status`
- **请求参数：**
  - **Path:** `id` (Integer, 文案ID)
  - **Body:**
    ```json
    {
      "status": 1 // 必填，0=待审核, 1=已通过, 2=已拒绝
    }
    ```
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "message": "文案状态已更新为：已通过"
  }
  ```
- **错误响应：**
  - `400 Bad Request`: `{"success": false, "message": "无效的状态值"}`
  - `404 Not Found`: `{"success": false, "message": "文案不存在"}`
- **认证/授权：** 需要审核员或更高级别权限

#### 2.2.4 批量更新文案状态（审核员）
- **接口概述：** 审核员或管理员批量更新多个文案的审核状态
- **请求方法：** `POST`
- **请求URL：** `/api/texts/batch-update`
- **请求参数 (Body)：**
  ```json
  {
    "ids": [1, 2, 3], // 必填，文案ID数组，不能为空
    "status": 1       // 必填，0=待审核, 1=已通过, 2=已拒绝
  }
  ```
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "data": {
      "affectedRows": 3
    },
    "message": "成功将 3 条文案状态更新为：已通过"
  }
  ```
- **认证/授权：** 需要审核员或更高级别权限

#### 2.2.5 获取统计信息
- **接口概述：** 获取文案的分类统计数据
- **请求方法：** `GET`
- **请求URL：** `/api/texts/stats`
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "data": {
      "total": 100,
      "pending": 20,
      "approved": 70,
      "rejected": 10
    },
    "message": "获取统计信息成功"
  }
  ```
- **认证/授权：** 需要认证

#### 2.2.6 获取最近文案
- **接口概述：** 获取最新提交的文案
- **请求方法：** `GET`
- **请求URL：** `/api/texts/recent`
- **请求参数 (Query)：**
  | 参数名  | 数据类型 | 是否必需 | 默认值 | 描述         |
  | ------- | -------- | -------- | ------ | ------------ |
  | `limit` | Integer  | 否       | 10     | 返回数量限制 |
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "data": [
      {
        "id": 1,
        "text": "最新文案内容",
        "user_nick": "作者A",
        "status": 0,
        "record_time": "2023-10-27T10:00:00.000Z"
      }
    ],
    "message": "获取最近文案成功"
  }
  ```
- **认证/授权：** 需要认证

### 2.3 用户管理接口 (User Management API)

**基础路径：** `/api/users`

#### 2.3.1 获取用户列表
- **接口概述：** 获取所有用户的列表，支持分页和筛选
- **请求方法：** `GET`
- **请求URL：** `/api/users`
- **请求参数 (Query)：**
  | 参数名   | 数据类型 | 是否必需 | 描述                                           |
  | -------- | -------- | -------- | ---------------------------------------------- |
  | `page`   | Number   | 否       | 页码                                           |
  | `size`   | Number   | 否       | 每页数量 (最大200)                             |
  | `search` | String   | 否       | 关键词 (匹配`username`或`nickname`)            |
  | `role`   | String   | 否       | 按角色筛选 (`reviewer`, `admin`, `superadmin`) |
  | `status` | String   | 否       | 按状态筛选 (`0`, `1`)                          |
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "message": "获取用户列表成功",
    "data": {
      "users": [
        {
          "id": 2,
          "username": "user1",
          "nickname": "用户1",
          "role": "reviewer",
          "status": 1,
          "created_at": "2023-10-26T10:00:00.000Z",
          "updated_at": "2023-10-26T10:00:00.000Z"
        }
      ],
      "total": 15,
      "page": 1,
      "size": 20
    }
  }
  ```
- **认证/授权：** 需要管理员权限

#### 2.3.2 获取单个用户信息
- **接口概述：** 根据ID获取单个用户的详细信息
- **请求方法：** `GET`
- **请求URL：** `/api/users/:id`
- **请求参数 (Path)：**
  | 参数名 | 数据类型 | 描述     |
  | ------ | -------- | -------- |
  | `id`   | Integer  | 用户的ID |
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "data": {
      "id": 2,
      "username": "user1",
      "nickname": "用户1",
      "role": "reviewer",
      "status": 1,
      "created_at": "2023-10-26T10:00:00.000Z",
      "updated_at": "2023-10-26T10:00:00.000Z"
    },
    "message": "获取用户信息成功"
  }
  ```
- **错误响应：**
  - `404 Not Found`: `{"success": false, "message": "用户不存在"}`
- **认证/授权：** 需要管理员权限

#### 2.3.3 创建用户
- **接口概述：** 管理员创建一个新的用户账户
- **请求方法：** `POST`
- **请求URL：** `/api/users`
- **请求参数 (Body)：**
  ```json
  {
    "username": "string", // 必填，3-20位字母/数字/下划线
    "password": "string", // 必填，最小6位
    "nickname": "string", // 必填，2-20位
    "role": "reviewer",   // 必填，reviewer/admin/superadmin，默认reviewer
    "status": 1           // 必填，0=禁用, 1=启用，默认1
  }
  ```
- **响应 (成功 201 Created)：**
  ```json
  {
    "success": true,
    "message": "用户创建成功",
    "data": {
      "id": 11,
      "username": "newadmin",
      "nickname": "新管理员",
      "role": "admin",
      "status": 1
    }
  }
  ```
- **错误响应：**
  - `400 Bad Request`: `{"success": false, "message": "用户名已存在"}`
- **认证/授权：** 需要管理员权限

#### 2.3.4 更新用户信息
- **接口概述：** 管理员更新指定用户的信息
- **请求方法：** `PUT`
- **请求URL：** `/api/users/:id`
- **请求参数：**
  - **Path:** `id` (Integer, 用户ID)
  - **Body:**
    ```json
    {
      "nickname": "string", // 必填，2-20位
      "role": "reviewer",   // 必填，reviewer/admin/superadmin
      "status": 1           // 必填，0=禁用, 1=启用
    }
    ```
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "message": "用户信息更新成功"
  }
  ```
- **错误响应：**
  - `403 Forbidden`: `{"success": false, "message": "不能修改管理员角色"}`
  - `404 Not Found`: `{"success": false, "message": "用户不存在"}`
- **认证/授权：** 需要管理员权限（非superadmin不能修改admin用户）

#### 2.3.5 更新用户密码
- **接口概述：** 管理员重置指定用户的密码
- **请求方法：** `PUT`
- **请求URL：** `/api/users/:id/password`
- **请求参数：**
  - **Path:** `id` (Integer, 用户ID)
  - **Body:**
    ```json
    {
      "password": "string" // 必填，最小6位
    }
    ```
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "message": "密码更新成功"
  }
  ```
- **认证/授权：** 需要管理员权限

#### 2.3.6 更新用户状态
- **接口概述：** 管理员更新指定用户的状态
- **请求方法：** `PUT`
- **请求URL：** `/api/users/:id/status`
- **请求参数：**
  - **Path:** `id` (Integer, 用户ID)
  - **Body:**
    ```json
    {
      "status": 1 // 必填，0=禁用, 1=启用
    }
    ```
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "message": "用户状态更新成功"
  }
  ```
- **认证/授权：** 需要管理员权限（非superadmin不能修改admin用户状态）

#### 2.3.7 删除用户
- **接口概述：** 管理员删除一个用户
- **请求方法：** `DELETE`
- **请求URL：** `/api/users/:id`
- **请求参数 (Path)：**
  | 参数名 | 数据类型 | 描述     |
  | ------ | -------- | -------- |
  | `id`   | Integer  | 用户的ID |
- **响应 (成功 200 OK)：**
  ```json
  {
    "success": true,
    "message": "用户删除成功"
  }
  ```
- **错误响应：**
  - `403 Forbidden`: `{"success": false, "message": "不能删除管理员账户"}`
- **认证/授权：** 需要管理员权限（不能删除admin或superadmin角色用户，superadmin可以删除admin）

---

## 3. 前端功能与后端API对应关系

### 3.1 用户认证模块

| 前端功能                | 对应API接口                         | 说明                    |
| ----------------------- | ----------------------------------- | ----------------------- |
| 登录页面 - 登录功能     | `POST /api/auth/login`              | 用户登录验证            |
| 登录页面 - 注册功能     | `POST /api/auth/register`           | 新用户注册              |
| 登录页面 - 注册状态检查 | `GET /api/auth/registration-status` | 检查注册功能是否开启    |
| 修改密码页面            | `POST /api/auth/change-password`    | 用户修改密码            |
| 主布局 - 用户信息显示   | `GET /api/auth/me`                  | 获取当前用户信息        |
| 主布局 - 退出登录       | 无需API                             | 清除本地token和用户信息 |

### 3.2 仪表盘模块

| 前端功能          | 对应API接口                        | 说明                       |
| ----------------- | ---------------------------------- | -------------------------- |
| 仪表盘 - 文案统计 | `GET /api/texts/stats`             | 获取文案分类统计           |
| 仪表盘 - 用户统计 | `GET /api/auth/registration-stats` | 获取用户注册统计（管理员） |
| 仪表盘 - 最近活动 | `GET /api/texts/recent`            | 获取最近文案活动           |

### 3.3 文案管理模块

| 前端功能            | 对应API接口                    | 说明                                 |
| ------------------- | ------------------------------ | ------------------------------------ |
| 文案列表 - 获取列表 | `GET /api/texts`               | 获取文案列表（支持分页、筛选、排序） |
| 文案列表 - 查看详情 | `GET /api/texts/:id`           | 获取单个文案详情                     |
| 文案列表 - 单个审核 | `PUT /api/texts/:id/status`    | 更新单个文案状态（审核员）           |
| 文案列表 - 批量审核 | `POST /api/texts/batch-update` | 批量更新文案状态（审核员）           |
| 文案列表 - 统计信息 | `GET /api/texts/stats`         | 获取文案统计信息                     |

### 3.4 用户管理模块（仅管理员）

| 前端功能             | 对应API接口                        | 说明                           |
| -------------------- | ---------------------------------- | ------------------------------ |
| 用户列表 - 获取列表  | `GET /api/users`                   | 获取用户列表（支持分页、筛选） |
| 用户列表 - 查看详情  | `GET /api/users/:id`               | 获取单个用户详情               |
| 用户列表 - 创建用户  | `POST /api/users`                  | 创建新用户                     |
| 用户列表 - 编辑用户  | `PUT /api/users/:id`               | 更新用户信息                   |
| 用户列表 - 重置密码  | `PUT /api/users/:id/password`      | 重置用户密码                   |
| 用户列表 - 启用/禁用 | `PUT /api/users/:id/status`        | 更新用户状态                   |
| 用户列表 - 删除用户  | `DELETE /api/users/:id`            | 删除用户                       |
| 用户统计             | `GET /api/auth/registration-stats` | 获取用户注册统计               |

### 3.5 系统设置模块（仅管理员）

| 前端功能            | 对应API接口                          | 说明              |
| ------------------- | ------------------------------------ | ----------------- |
| 注册设置 - 获取状态 | `GET /api/auth/registration-status`  | 获取注册功能状态  |
| 注册设置 - 切换状态 | `POST /api/auth/toggle-registration` | 开启/关闭注册功能 |
| 注册设置 - 统计信息 | `GET /api/auth/registration-stats`   | 获取注册统计信息  |

### 3.6 权限控制说明

| 用户角色     | 可访问功能                 | 权限说明                                       |
| ------------ | -------------------------- | ---------------------------------------------- |
| `reviewer`   | 仪表盘、文案管理、修改密码 | 可查看和审核文案                               |
| `admin`      | 所有功能                   | 可管理用户和系统设置，但不能操作superadmin用户 |
| `superadmin` | 所有功能                   | 最高权限，可操作所有用户                       |

---

## 4. 端口配置说明

### 4.1 开发环境端口配置

#### 4.1.1 前端开发服务器
- **端口：** `30011`
- **配置文件：** `vite.config.js`
- **配置内容：**
  ```javascript
  export default defineConfig({
    server: {
      port: 30011,
      proxy: {
        '/api': {
          target: 'http://127.0.0.1:30010',
          changeOrigin: true
        }
      }
    }
  })
  ```
- **说明：** 前端开发服务器运行在30011端口，并将所有`/api`请求代理到后端服务器

#### 4.1.2 后端服务器
- **端口：** `30010`
- **配置文件：** `.env.development`
- **配置内容：**
  ```env
  PORT=30010
  ```
- **说明：** 后端Express服务器运行在30010端口

#### 4.1.3 CORS配置
- **配置文件：** `.env.development`
- **配置内容：**
  ```env
  CORS_ORIGINS=http://localhost:30011,http://text.guildbot.cn
  ```
- **说明：** 后端允许来自前端开发服务器和生产域名的跨域请求

### 4.2 生产环境端口配置

#### 4.2.1 前端应用
- **部署方式：** 静态文件部署
- **Web服务器：** Nginx（推荐）
- **端口：** 通常为80（HTTP）或443（HTTPS）
- **说明：** 前端构建后的静态文件通过Web服务器提供服务

#### 4.2.2 后端服务
- **端口：** 可配置（通过环境变量`PORT`）
- **反向代理：** 通过Nginx代理到后端服务
- **API路径：** `/api/*`
- **说明：** 生产环境中后端服务通常不直接暴露，而是通过反向代理访问

#### 4.2.3 Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理到后端
    location /api/ {
        proxy_pass http://localhost:30010;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 4.3 端口冲突解决

#### 4.3.1 检查端口占用
```bash
# Windows
netstat -ano | findstr :30010
netstat -ano | findstr :30011

# Linux/Mac
lsof -i :30010
lsof -i :30011
```

#### 4.3.2 修改端口配置
1. **修改后端端口：**
   - 编辑`.env.development`文件中的`PORT`值
   - 同时更新`vite.config.js`中的代理目标地址

2. **修改前端端口：**
   - 编辑`vite.config.js`文件中的`server.port`值
   - 同时更新`.env.development`中的`CORS_ORIGINS`配置

### 4.4 启动命令

#### 4.4.1 开发环境启动
```bash
# 同时启动前端和后端
npm run dev:full

# 或分别启动
npm run dev        # 启动前端开发服务器
npm run server     # 启动后端服务器
```

#### 4.4.2 生产环境启动
```bash
# 构建前端
npm run build

# 启动后端生产服务器
npm start
```

---

## 5. 前端API基地址自动解析

### 5.1 自动解析机制

前端应用通过`src/utils/request.js`文件实现API基地址的自动解析，无需通过环境变量进行配置。解析逻辑如下：

#### 5.1.1 解析规则
```javascript
// 获取当前页面的hostname
const hostname = window.location.hostname;
const protocol = window.location.protocol;
const port = window.location.port;

let baseURL;

// 开发环境检测
if (hostname === 'localhost' || hostname === '127.0.0.1') {
  // 本地开发环境
  baseURL = `${protocol}//${hostname}:30010/api`;
} else if (
  hostname.startsWith('192.168.') || 
  hostname.startsWith('10.') || 
  (hostname.startsWith('172.') && 
   parseInt(hostname.split('.')[1]) >= 16 && 
   parseInt(hostname.split('.')[1]) <= 31)
) {
  // 局域网环境
  baseURL = `${protocol}//${hostname}:30010/api`;
} else {
  // 生产环境
  baseURL = `${protocol}//${hostname}${port ? ':' + port : ''}/api`;
}
```

#### 5.1.2 环境判断逻辑

1. **本地开发环境：**
   - 检测hostname为`localhost`或`127.0.0.1`
   - API基地址：`http(s)://localhost:30010/api`或`http(s)://127.0.0.1:30010/api`

2. **局域网开发环境：**
   - 检测hostname为局域网IP地址：
     - `192.168.x.x`（C类私有地址）
     - `10.x.x.x`（A类私有地址）
     - `172.16.x.x` - `172.31.x.x`（B类私有地址）
   - API基地址：`http(s)://[局域网IP]:30010/api`

3. **生产环境：**
   - 其他所有hostname（公网域名）
   - API基地址：`http(s)://[域名]:[端口]/api`
   - 端口部分：如果当前页面有端口号则包含，否则省略

### 5.2 实现代码

#### 5.2.1 完整的request.js文件
```javascript
import axios from 'axios';
import { ElMessage } from 'element-plus';
import router from '@/router';

// 自动解析API基地址
function getBaseURL() {
  const hostname = window.location.hostname;
  const protocol = window.location.protocol;
  const port = window.location.port;
  
  // 本地开发环境
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return `${protocol}//${hostname}:30010/api`;
  }
  
  // 局域网环境
  if (
    hostname.startsWith('192.168.') || 
    hostname.startsWith('10.') || 
    (hostname.startsWith('172.') && 
     parseInt(hostname.split('.')[1]) >= 16 && 
     parseInt(hostname.split('.')[1]) <= 31)
  ) {
    return `${protocol}//${hostname}:30010/api`;
  }
  
  // 生产环境
  return `${protocol}//${hostname}${port ? ':' + port : ''}/api`;
}

// 创建axios实例
const request = axios.create({
  baseURL: getBaseURL(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { data } = response;
    
    // 检查业务状态码
    if (data.success) {
      return data;
    } else {
      ElMessage.error(data.message || '请求失败');
      return Promise.reject(new Error(data.message || '请求失败'));
    }
  },
  (error) => {
    // HTTP错误处理
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录');
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          router.push({
            path: '/login',
            query: { redirect: router.currentRoute.value.fullPath }
          });
          break;
        case 403:
          ElMessage.error('权限不足');
          break;
        case 404:
          ElMessage.error('请求的资源不存在');
          break;
        case 500:
          ElMessage.error('服务器内部错误');
          break;
        default:
          ElMessage.error(data?.message || '请求失败');
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败');
    } else {
      ElMessage.error('请求配置错误');
    }
    
    return Promise.reject(error);
  }
);

export default request;
```

### 5.3 使用示例

#### 5.3.1 API模块中的使用
```javascript
// src/api/auth.js
import request from '@/utils/request';

export const authAPI = {
  // 登录
  login: (data) => request.post('/auth/login', data),
  
  // 注册
  register: (data) => request.post('/auth/register', data),
  
  // 获取当前用户信息
  getCurrentUser: () => request.get('/auth/me'),
  
  // 修改密码
  changePassword: (data) => request.post('/auth/change-password', data)
};
```

#### 5.3.2 组件中的使用
```javascript
// 在Vue组件中使用
import { authAPI } from '@/api/auth';

export default {
  methods: {
    async handleLogin() {
      try {
        const response = await authAPI.login({
          username: this.username,
          password: this.password
        });
        
        // 处理登录成功
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('user', JSON.stringify(response.data.user));
        
        this.$router.push('/');
      } catch (error) {
        // 错误已在拦截器中处理
        console.error('登录失败:', error);
      }
    }
  }
};
```

### 5.4 优势特点

#### 5.4.1 自动适配
- **无需配置：** 不需要设置环境变量或配置文件
- **智能识别：** 自动识别开发、测试、生产环境
- **灵活部署：** 支持不同的部署方式和域名

#### 5.4.2 开发友好
- **本地开发：** 自动连接到本地后端服务
- **局域网测试：** 支持局域网内的设备访问
- **团队协作：** 团队成员无需额外配置

#### 5.4.3 生产可靠
- **域名适配：** 自动适配生产环境域名
- **端口处理：** 智能处理端口号
- **协议支持：** 同时支持HTTP和HTTPS

### 5.5 调试和验证

#### 5.5.1 控制台调试
```javascript
// 在浏览器控制台中查看当前API基地址
console.log('当前API基地址:', axios.defaults.baseURL);

// 或者直接调用解析函数
function getBaseURL() {
  const hostname = window.location.hostname;
  const protocol = window.location.protocol;
  const port = window.location.port;
  
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return `${protocol}//${hostname}:30010/api`;
  }
  
  if (
    hostname.startsWith('192.168.') || 
    hostname.startsWith('10.') || 
    (hostname.startsWith('172.') && 
     parseInt(hostname.split('.')[1]) >= 16 && 
     parseInt(hostname.split('.')[1]) <= 31)
  ) {
    return `${protocol}//${hostname}:30010/api`;
  }
  
  return `${protocol}//${hostname}${port ? ':' + port : ''}/api`;
}

console.log('解析的API基地址:', getBaseURL());
```

#### 5.5.2 网络请求验证
```javascript
// 测试API连接
import request from '@/utils/request';

// 测试获取注册状态（无需认证的接口）
request.get('/auth/registration-status')
  .then(response => {
    console.log('API连接正常:', response);
  })
  .catch(error => {
    console.error('API连接失败:', error);
  });
```